<template>
  <a-spin :spinning="confirmLoading">
    <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol">
      <a-row>
        <a-col :span="12">
          <a-form-item label="所在单位" v-bind="validateInfos.companyId">
            <j-async-search-select
              placeholder="请选择所在单位"
              v-model:value="formData.companyId"
              @select="setName"
              dict="company where del_flag=0 and pid=0,name,id"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="预约名称" v-bind="validateInfos.regName">
            <a-input v-model:value="formData.regName" placeholder="请输入预约名称" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="体检类别" v-bind="validateInfos.examType">
            <j-dict-select-tag v-model:value="formData.examType" dict-code="examination_type" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="人员类别" v-bind="validateInfos.personCategory">
            <j-dict-select-tag
              v-model:value="formData.personCategory"
              dictCode="customer_exam_type"
              placeholder="请选择人员类别"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="单位负责人" v-bind="validateInfos.linkMan">
            <a-input v-model:value="formData.linkMan" placeholder="请输入单位负责人" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="开始日期" v-bind="validateInfos.startCheckDate">
            <a-date-picker
              placeholder="请选择开始日期"
              v-model:value="formData.startCheckDate"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="结束日期" v-bind="validateInfos.endCheckDate">
            <a-date-picker
              placeholder="请选择结束日期"
              v-model:value="formData.endCheckDate"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="预约人数" v-bind="validateInfos.personCount">
            <a-input-number v-model:value="formData.personCount" placeholder="请输入预约人数" style="width: 100%" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="客服专员" v-bind="validateInfos.serviceManager">
            <j-select-user-by-dept v-model:value="formData.serviceManager" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="打印导引单" v-bind="validateInfos.printGuidance">
            <j-switch v-model:value="formData.printGuidance" :options="[1, 0]" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="打印申请单" v-bind="validateInfos.printApply">
            <j-switch v-model:value="formData.printApply" :options="[1, 0]" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="封账状态" v-bind="validateInfos.checkoutStatus">
            <j-switch v-model:value="formData.checkoutStatus" :options="[1, 0]" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="锁定状态" v-bind="validateInfos.lockStatus">
            <j-switch v-model:value="formData.lockStatus" :options="[1, 0]" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="formData.createBy">
          <a-form-item label="创建人" v-bind="validateInfos.createBy">
            {{ formData.createBy }}
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="团报编号" v-bind="validateInfos.companyReportNo">
            <a-input v-model:value="formData.companyReportNo" placeholder="请输入团报编号" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="团报日期" v-bind="validateInfos.companyReportDate">
            <a-date-picker
              placeholder="请选择团报日期"
              v-model:value="formData.companyReportDate"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="检查依据" v-bind="validateInfos.according">
            <a-textarea v-model:value="formData.according" :rows="4" placeholder="请输入检查依据" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="处理意见" v-bind="validateInfos.opinion">
            <a-textarea v-model:value="formData.opinion" :rows="4" placeholder="请输入处理意见" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="备注" v-bind="validateInfos.remark">
            <a-textarea v-model:value="formData.remark" :rows="4" placeholder="请输入备注" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="报告显示单位名称">
            <j-switch v-model:value="formData.cnameDisplayInReport" :options="[1, 0]" :disabled="disabled" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
  import { computed, defineExpose, inject, nextTick, reactive, ref } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';
  import JSelectUserByDept from '/@/components/Form/src/jeecg/components/JSelectUserByDept.vue';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate } from '../CompanyReg.api';
  import { Form } from 'ant-design-vue';
  import { duplicateValidate } from '/@/utils/helper/validator';
  import dayjs from 'dayjs';
  import { JAsyncSearchSelect } from '@/components/Form';
  import { companyRegIdKey, companyRegKey } from '@/providekey/provideKeys';

  const companyRegId = inject(companyRegIdKey, { value: {}, setValue: () => {} });
  const companyReg = inject(companyRegKey, { value: {}, setValue: () => {} });
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => {} },
    formBpm: { type: Boolean, default: true },
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    companyId: '',
    regName: '',
    examType: '',
    personCategory: '',
    linkMan: '',
    startCheckDate: '',
    endCheckDate: '',
    personCount: undefined,
    serviceManager: '',
    checkoutStatus: undefined,
    lockStatus: undefined,
    createBy: '',
    companyReportNo: '',
    companyReportDate: '',
    according: '',
    opinion: '',
    remark: '',
    cnameDisplayInReport: '1',
    printGuidance: '1',
    printApply: '1',
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({
    companyId: [{ required: true, message: '请输入所在单位!' }],
    regName: [{ required: true, message: '请输入预约名称!' }, { validator: regNameDuplicatevalidate }],
    examType: [{ required: true, message: '请输入体检类别!' }],
    startCheckDate: [{ required: true, message: '请输入开始日期!' }],
    endCheckDate: [{ required: true, message: '请输入结束日期!' }],
    personCount: [
      { required: true, message: '请输入预约人数!' },
      { pattern: /^-?\d+$/, message: '请输入整数!' },
    ],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return false;
      } else {
        return true;
      }
    }
    return props.formDisabled;
  });

  function setName(selectedOption) {
    if (selectedOption?.length > 0 && !formData.regName) {
      formData.regName = dayjs().format('YYYY') + selectedOption[0].text;
      formData.companyName = selectedOption[0].text;
    }
  }
  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      //赋值
      Object.assign(formData, record);
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    // 触发表单验证
    await validate();
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }

    //model.helpChar = PinyinUtil.getInitials(model.regName);
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          Object.assign(formData, res.result);
          createMessage.success(res.message);
          companyReg.setValue(res.result);
          emit('ok', res.result);
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  async function regNameDuplicatevalidate(_r, value) {
    return duplicateValidate('company_reg', 'reg_name', value, formData.id || '');
  }

  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    height: 500px !important;
    overflow-y: auto;
    padding: 14px;
  }
</style>

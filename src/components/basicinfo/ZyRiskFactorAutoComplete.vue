<template>
  <div class="zy-risk-factor-autocomplete">
    <a-auto-complete
      v-model:value="inputValue"
      :options="options"
      :placeholder="placeholder"
      :allowClear="allowClear"
      :disabled="disabled"
      :loading="loading"
      @search="handleSearch"
      @select="handleSelect"
      @change="handleChange"
      @clear="handleClear"
      @blur="handleBlur"
      @keydown="handleKeyDown"
      :filterOption="false"
      :notFoundContent="loading ? '搜索中...' : '暂无数据'"
    >
      <template #option="{ value: val, label, data }">
        <div class="risk-factor-option">
          <div class="risk-factor-name">{{ data.name }}</div>
          <div class="risk-factor-details">
            <span v-if="data.code && config.showCode" class="risk-factor-code">编码: {{ data.code }}</span>
            <span v-if="data.helpChar && config.showHelpChar" class="risk-factor-help-char">{{ data.helpChar }}</span>
            <span v-if="data.wubiChar && config.showWubiChar" class="risk-factor-wubi-char">五笔: {{ data.wubiChar }}</span>
          </div>
        </div>
      </template>
    </a-auto-complete>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch, computed, onMounted, nextTick } from 'vue';
  import { debounce } from 'lodash-es';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    getZyRiskFactorAutoComplete,
    exactMatchZyRiskFactorByName,
    getFallbackZyRiskFactor,
    saveOrUpdate
  } from '/@/api/basicinfo/zyRiskFactor';
  import { cleanRiskFactorData, detectDataIssues } from '/@/utils/zyRiskFactorUtils';
  import type {
    ZyRiskFactorAutoCompleteDTO,
    ZyRiskFactorAutoCompleteOption,
    ZyRiskFactorSimpleConfig,
    ZyRiskFactorSimpleChangeEvent,
    ZyRiskFactor,
  } from '/@/types/basicinfo/zyRiskFactor';

  interface Props {
    value?: string;
    placeholder?: string;
    allowClear?: boolean;
    disabled?: boolean;
    config?: Partial<ZyRiskFactorSimpleConfig>;
  }

  interface Emits {
    (e: 'update:value', value: string): void;
    (e: 'change', event: ZyRiskFactorSimpleChangeEvent): void;
    (e: 'select', value: string, option: ZyRiskFactorAutoCompleteDTO): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '请输入危害因素名称、编码或助记码',
    allowClear: true,
    disabled: false,
    config: () => ({}),
  });

  const emit = defineEmits<Emits>();
  const { createMessage } = useMessage();

  // 默认配置
  const defaultConfig: ZyRiskFactorSimpleConfig = {
    enableAutoAdd: true,
    searchDebounce: 300,
    maxOptions: 50,
    showCode: true,
    showHelpChar: true,
    showWubiChar: false,
  };

  // 合并配置
  const config = computed(() => ({ ...defaultConfig, ...props.config }));

  const inputValue = ref<string>('');
  const options = ref<ZyRiskFactorAutoCompleteOption[]>([]);
  const loading = ref<boolean>(false);
  const riskFactorList = ref<ZyRiskFactorAutoCompleteDTO[]>([]);
  const isManualInput = ref<boolean>(false);
  const isSpaceKeySelect = ref<boolean>(false); // 标记是否为空格键选中
  const selectedFactors = ref<ZyRiskFactorAutoCompleteDTO[]>([]); // 维护已选中的危害因素列表

  // 监听外部传入的value变化
  watch(
    () => props.value,
    async (newVal) => {
      if (newVal !== inputValue.value) {
        inputValue.value = newVal || '';
        // 当外部值变化时，重新同步已选中的危害因素列表
        if (newVal) {
          await syncSelectedFactorsFromInput(newVal);
        } else {
          selectedFactors.value = [];
        }
      }
    },
    { immediate: true }
  );

  // 监听输入值变化，向外部发送更新
  watch(inputValue, (newVal) => {
    emit('update:value', newVal);
  });

  /**
   * 获取危害因素列表数据
   */
  const fetchRiskFactorList = async (keyword?: string, searchType: 'name' | 'code' | 'helpChar' | 'wubiChar' | 'all' = 'all') => {
    try {
      loading.value = true;
      console.log('ZyRiskFactorAutoComplete: 开始获取危害因素列表，关键词:', keyword, '搜索类型:', searchType);

      const response = await getZyRiskFactorAutoComplete(keyword, config.value.maxOptions, searchType);
      console.log('ZyRiskFactorAutoComplete: API响应:', response);

      if (response && response.success && response.result) {
        riskFactorList.value = response.result;
        updateOptions(keyword);
        console.log('ZyRiskFactorAutoComplete: 成功加载', response.result.length, '条数据');

        // 如果是降级模式，显示提示信息
        if (response.message && response.message.includes('降级模式')) {
          console.info('ZyRiskFactorAutoComplete: 使用降级模式加载数据');
        }
      } else {
        console.warn('ZyRiskFactorAutoComplete: API响应格式异常:', response);
        riskFactorList.value = [];
        options.value = [];
      }
    } catch (error: any) {
      console.error('ZyRiskFactorAutoComplete: 获取危害因素列表失败:', error);

      // 检查是否是缓存相关错误
      if (error?.message && error.message.includes('cache')) {
        console.warn('ZyRiskFactorAutoComplete: 检测到缓存错误，这是后端配置问题');
      }

      riskFactorList.value = [];
      options.value = [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新选项列表
   */
  const updateOptions = (keyword?: string) => {
    // 使用工具函数清理数据
    const { cleaned, filtered } = cleanRiskFactorData(riskFactorList.value, keyword);

    // 记录被过滤的数据
    if (filtered.length > 0) {
      console.warn('ZyRiskFactorAutoComplete: 过滤了以下无效数据:', filtered);
      filtered.forEach(({ factor, reason }) => {
        console.warn(`- ${factor.name} (ID: ${factor.id}): ${reason}`);
      });
    }

    // 检测数据问题
    const dataIssues = detectDataIssues(cleaned);
    if (dataIssues.issues.length > 0) {
      console.warn('ZyRiskFactorAutoComplete: 检测到数据质量问题:', dataIssues.issues);

      // 如果有可疑名称，额外记录
      if (dataIssues.suspiciousNames.length > 0) {
        console.warn(
          'ZyRiskFactorAutoComplete: 可疑名称列表:',
          dataIssues.suspiciousNames.map((f) => f.name)
        );
      }
    }

    // 生成选项列表
    options.value = cleaned.map((item) => ({
      value: item.id,
      label: item.name,
      data: item,
    }));

    console.log('ZyRiskFactorAutoComplete: 更新选项列表，原始数据', riskFactorList.value.length, '条，清理后', options.value.length, '个选项');

    // 如果过滤后数据明显减少，显示用户友好的提示
    if (riskFactorList.value.length > 0 && filtered.length > riskFactorList.value.length * 0.2) {
      console.warn('ZyRiskFactorAutoComplete: 大量数据被过滤，建议检查后端数据质量');

      // 在开发环境下，可以显示更详细的信息
      if (process.env.NODE_ENV === 'development') {
        createMessage.warning(`数据质量提醒：${filtered.length}条数据被过滤，请检查控制台日志`);
      }
    }
  };

  /**
   * 提取最后一个词作为搜索关键词
   */
  const extractLastWord = (input: string): string => {
    if (!input) return '';
    const words = input.trim().split(/\s+/);
    return words[words.length - 1] || '';
  };



  /**
   * 设置光标位置到输入框末尾
   */
  const setCursorToEnd = () => {
    nextTick(() => {
      const inputElement = document.querySelector('.zy-risk-factor-autocomplete .ant-input') as HTMLInputElement;
      if (inputElement) {
        inputElement.focus();
        inputElement.setSelectionRange(inputElement.value.length, inputElement.value.length);
      }
    });
  };

  /**
   * 添加危害因素到已选中列表
   */
  const addToSelectedFactors = (factor: ZyRiskFactorAutoCompleteDTO) => {
    // 检查是否已存在（根据名称判断）
    const existingIndex = selectedFactors.value.findIndex(f => f.name === factor.name);
    if (existingIndex >= 0) {
      // 如果已存在，更新为新的信息（可能有更完整的ID等）
      selectedFactors.value[existingIndex] = factor;
    } else {
      // 如果不存在，添加到列表
      selectedFactors.value.push(factor);
    }
  };

  /**
   * 过滤掉临时数据，只返回有效的危害因素
   */
  const getValidSelectedFactors = (): ZyRiskFactorAutoCompleteDTO[] => {
    return selectedFactors.value.filter(factor =>
      !factor.id.startsWith('temp_') || factor.code.startsWith('AUTO_')
    );
  };

  /**
   * 根据输入值同步已选中的危害因素列表
   * 这个函数用于处理用户直接编辑输入框的情况
   */
  const syncSelectedFactorsFromInput = async (inputStr: string) => {
    if (!inputStr.trim()) {
      selectedFactors.value = [];
      return;
    }

    const words = inputStr.trim().split(/\s+/);
    const newSelectedFactors: ZyRiskFactorAutoCompleteDTO[] = [];

    // 对于每个词，尝试在现有的selectedFactors中找到匹配的
    for (const word of words) {
      const existing = selectedFactors.value.find(f => f.name === word);
      if (existing) {
        // 如果找到了，使用现有的完整信息
        newSelectedFactors.push(existing);
      } else {
        // 如果没找到，尝试从后端查找精确匹配
        try {
          const exactMatchResponse = await exactMatchZyRiskFactorByName(word);
          if (exactMatchResponse?.success && exactMatchResponse.result) {
            // 找到精确匹配，使用真实数据
            const matchedFactor = exactMatchResponse.result;
            newSelectedFactors.push({
              id: matchedFactor.id,
              name: matchedFactor.name,
              code: matchedFactor.code,
              helpChar: matchedFactor.helpChar,
              wubiChar: matchedFactor.wubiChar,
            });
          } else {
            // 没找到匹配，创建临时对象（但不包含在最终结果中）
            console.warn(`未找到危害因素: ${word}，将在确认时处理`);
            newSelectedFactors.push({
              id: `temp_${word}`,
              name: word,
              code: `TEMP_${Date.now()}`, // 临时code
              helpChar: '',
              wubiChar: '',
            });
          }
        } catch (error) {
          console.warn(`查找危害因素失败: ${word}`, error);
          // 创建临时对象
          newSelectedFactors.push({
            id: `temp_${word}`,
            name: word,
            code: `TEMP_${Date.now()}`, // 临时code
            helpChar: '',
            wubiChar: '',
          });
        }
      }
    }

    selectedFactors.value = newSelectedFactors;
  };

  /**
   * 防抖搜索处理
   */
  const debouncedSearch = debounce(async (keyword: string) => {
    await fetchRiskFactorList(keyword);
  }, config.value.searchDebounce);

  /**
   * 搜索处理
   */
  const handleSearch = (value: string) => {
    isManualInput.value = true;

    if (!value || value.trim().length === 0) {
      // 如果搜索为空，显示默认数据
      fetchRiskFactorList();
    } else {
      // 多词输入时，取最后一个词作为搜索关键词
      const searchKeyword = extractLastWord(value);
      if (searchKeyword) {
        debouncedSearch(searchKeyword);
      } else {
        fetchRiskFactorList();
      }
    }
  };

  /**
   * 智能更新已选中的危害因素列表
   * 当用户选中一个选项时，智能地更新整个输入内容对应的因素列表
   */
  const smartUpdateSelectedFactors = async (newValue: string, selectedFactor: ZyRiskFactorAutoCompleteDTO) => {
    console.log('🔍 smartUpdateSelectedFactors 开始');
    console.log('🔍 新值:', newValue);
    console.log('🔍 选中的因素:', selectedFactor);

    // 先同步输入值对应的因素列表
    await syncSelectedFactorsFromInput(newValue);

    // 确保选中的因素在列表中（可能需要更新为完整信息）
    addToSelectedFactors(selectedFactor);

    console.log('🔍 更新后的因素列表:', selectedFactors.value.map(f => f.name));
  };

  /**
   * 选择处理
   */
  const handleSelect = async (value: string, option: any) => {
    const selectedFactor = option.data as ZyRiskFactorAutoCompleteDTO;
    isManualInput.value = false;

    let newValue: string;
    const currentInput = inputValue.value;

    if (isSpaceKeySelect.value) {
      // 空格键选中：替换最后一个词并添加空格
      const words = currentInput.trim().split(/\s+/);
      words[words.length - 1] = selectedFactor.name;
      newValue = words.join(' ') + ' '; // 末尾添加空格
      isSpaceKeySelect.value = false; // 重置标记
    } else {
      // 点击选中或回车选中：判断是否为多词输入
      const trimmed = currentInput.trim();
      if (trimmed.includes(' ')) {
        // 多词输入：替换最后一个词
        const words = trimmed.split(/\s+/);
        words[words.length - 1] = selectedFactor.name;
        newValue = words.join(' ');
      } else {
        // 单词输入：直接显示选中的名称
        newValue = selectedFactor.name;
      }
    }

    inputValue.value = newValue;

    // 智能更新已选中的危害因素列表
    await smartUpdateSelectedFactors(newValue, selectedFactor);

    // 设置光标位置到末尾，便于继续输入
    setCursorToEnd();

    const event: ZyRiskFactorSimpleChangeEvent = {
      value: newValue,
      selectedFactor,
      selectedFactors: getValidSelectedFactors(), // 返回过滤后的有效列表
      isManualInput: false,
      isNewlyCreated: false,
    };

    console.log('🔍 发送change事件:', { value: newValue, selectedFactors: getValidSelectedFactors() });
    emit('change', event);
    emit('select', value, selectedFactor);
  };

  /**
   * 值变化处理
   */
  const handleChange = (value: string) => {
    if (!value) {
      selectedFactors.value = [];
      const event: ZyRiskFactorSimpleChangeEvent = {
        value: '',
        selectedFactors: [], // 为了兼容性，提供空数组
        isManualInput: false,
      };
      emit('change', event);
    }
  };

  /**
   * 清空处理
   */
  const handleClear = () => {
    inputValue.value = '';
    selectedFactors.value = [];
    isManualInput.value = false;

    const event: ZyRiskFactorSimpleChangeEvent = {
      value: '',
      selectedFactors: [], // 为了兼容性，提供空数组
      isManualInput: false,
    };
    emit('change', event);

    // 清空后重新加载默认数据
    fetchRiskFactorList();
  };

  /**
   * 键盘事件处理
   */
  const handleKeyDown = (event: KeyboardEvent) => {
    // 空格键处理逻辑
    if (event.key === ' ' && isManualInput.value && inputValue.value.trim()) {
      event.preventDefault();

      // 如果有候选选项，选中第一个
      if (options.value.length > 0) {
        const firstOption = options.value[0];
        isSpaceKeySelect.value = true; // 标记为空格键选中
        handleSelect(firstOption.value, { data: firstOption.data });
      } else {
        // 如果没有候选选项，进行自动添加
        handleAutoAdd();
      }
    }

    // 回车键处理逻辑
    if (event.key === 'Enter' && isManualInput.value && inputValue.value.trim()) {
      // 如果有候选选项，让AutoComplete组件自然处理选择
      if (options.value.length > 0) {
        // 不阻止默认行为，让AutoComplete自动选中第一个选项
        return;
      } else {
        // 如果没有候选选项，阻止默认行为并进行自动添加
        event.preventDefault();
        handleAutoAdd();
      }
    }
  };

  /**
   * 失焦处理 - 处理手动输入和自动添加
   */
  const handleBlur = async () => {
    if (!isManualInput.value || !inputValue.value.trim() || !config.value.enableAutoAdd) {
      return;
    }

    try {
      await handleAutoAdd();
    } catch (error: any) {
      console.error('自动添加处理失败:', error);
      createMessage.error('自动添加功能暂时不可用');
    }
  };

  /**
   * 处理自动添加新数据
   */
  const handleAutoAdd = async () => {
    const input = inputValue.value.trim();
    if (!input) return;

    // 对于多词输入，取最后一个词进行处理
    const lastWord = extractLastWord(input);
    if (!lastWord) return;

    try {
      loading.value = true;

      // 1. 先检查最后一个词是否已存在
      let exactMatchResponse: any = null;

      try {
        exactMatchResponse = await exactMatchZyRiskFactorByName(lastWord);
      } catch (error: any) {
        console.warn('精确匹配API调用失败:', error);
        // 继续执行，当作没有找到匹配
      }

      if (exactMatchResponse?.success && exactMatchResponse.result) {
        // 如果找到精确匹配，根据触发方式决定处理方式
        const matchedFactor = exactMatchResponse.result;
        let newValue: string;

        if (isSpaceKeySelect.value) {
          // 空格键触发：替换最后一个词并添加空格
          const words = input.split(/\s+/);
          words[words.length - 1] = matchedFactor.name;
          newValue = words.join(' ') + ' ';
          isSpaceKeySelect.value = false; // 重置标记
        } else {
          // 失焦/回车触发：确认当前输入，替换最后一个词为确认的名称
          const trimmedInput = input.trim();
          if (trimmedInput.includes(' ')) {
            // 多词输入：替换最后一个词为精确匹配的名称
            const words = trimmedInput.split(/\s+/);
            words[words.length - 1] = matchedFactor.name;
            newValue = words.join(' ');
          } else {
            // 单词输入：直接使用精确匹配的名称
            newValue = matchedFactor.name;
          }
        }

        inputValue.value = newValue;

        // 更新已选中的危害因素列表
        await syncSelectedFactorsFromInput(newValue);
        addToSelectedFactors(matchedFactor);

        setCursorToEnd();

        const event: ZyRiskFactorSimpleChangeEvent = {
          value: newValue,
          selectedFactor: matchedFactor,
          selectedFactors: getValidSelectedFactors(), // 返回过滤后的有效列表
          isManualInput: true, // 自动添加算作手动输入
          isNewlyCreated: false,
        };
        emit('change', event);
        createMessage.success('找到匹配的危害因素');
        return;
      }

      // 2. 如果不存在，获取fallback模板并创建新记录
      let fallbackData: ZyRiskFactorAutoCompleteDTO;

      try {
        const fallbackResponse = await getFallbackZyRiskFactor();

        if (fallbackResponse.success && fallbackResponse.result) {
          fallbackData = fallbackResponse.result;
        } else {
          throw new Error('Fallback API返回失败');
        }
      } catch (error: any) {
        console.warn('获取fallback数据失败，使用默认模板:', error);

        // 使用默认的fallback数据
        fallbackData = {
          id: 'default_fallback',
          name: '默认模板',
          code: 'DEFAULT',
          helpChar: 'MR',
          wubiChar: 'MRMB',
        };

        createMessage.warning('使用默认模板创建危害因素');
      }

      // 3. 创建新的危害因素记录（使用后端实体字段）
      const newRiskFactor = {
        name: lastWord,
        code: 'AUTO_' + Date.now(), // 为新选项生成唯一的code
        helpChar: fallbackData.helpChar || '',
        wubiCode: fallbackData.wubiChar || '', // 注意：后端字段是wubiCode
        remark: `基于用户输入"${lastWord}"自动创建，模板来源：${fallbackData.name}`,
        valid: 1, // 启用状态
        sysFlag: '0', // 非系统内置
        sort: 999, // 排序
        fallbackFlag: '0', // 非fallback记录
      };

      // 4. 保存新记录
      let newFactorDTO: ZyRiskFactorAutoCompleteDTO;

      try {
        const saveResponse = await saveOrUpdate(newRiskFactor, false);

        if (saveResponse && saveResponse.success) {
          // 5. 创建新的DTO对象用于返回
          newFactorDTO = {
            id: saveResponse.result?.id || Date.now().toString(),
            name: lastWord,
            code: newRiskFactor.code,
            helpChar: newRiskFactor.helpChar,
            wubiChar: newRiskFactor.wubiCode, // 注意字段名转换
          };
        } else {
          throw new Error('保存API返回失败');
        }
      } catch (error: any) {
        console.warn('保存新危害因素失败，使用临时数据:', error);

        // 创建临时的DTO对象，不保存到后端
        newFactorDTO = {
          id: `temp_new_${Date.now()}`,
          name: lastWord,
          code: newRiskFactor.code,
          helpChar: newRiskFactor.helpChar,
          wubiChar: newRiskFactor.wubiCode, // 注意字段名转换
        };

        createMessage.warning(`临时添加危害因素：${lastWord}（未保存到服务器）`);
      }

        // 6. 更新输入框值，根据触发方式决定处理方式
        let newValue: string;

        if (isSpaceKeySelect.value) {
          // 空格键触发：替换最后一个词并添加空格
          const words = input.split(/\s+/);
          words[words.length - 1] = lastWord;
          newValue = words.join(' ') + ' ';
          isSpaceKeySelect.value = false; // 重置标记
        } else {
          // 失焦/回车触发：拼接到现有内容后面
          const trimmedInput = input.trim();
          if (trimmedInput.includes(' ')) {
            // 多词输入：替换最后一个词
            const words = trimmedInput.split(/\s+/);
            words[words.length - 1] = lastWord;
            newValue = words.join(' ');
          } else {
            // 单词输入：直接替换
            newValue = lastWord;
          }
        }

        inputValue.value = newValue;

        // 更新已选中的危害因素列表
        await syncSelectedFactorsFromInput(newValue);
        addToSelectedFactors(newFactorDTO);

        setCursorToEnd();

        const event: ZyRiskFactorSimpleChangeEvent = {
          value: newValue,
          selectedFactor: newFactorDTO,
          selectedFactors: getValidSelectedFactors(), // 返回过滤后的有效列表
          isManualInput: true, // 自动添加算作手动输入
          isNewlyCreated: true,
        };
        emit('change', event);

        // 如果是临时数据，显示不同的消息
        if (newFactorDTO.id.startsWith('temp_new_')) {
          createMessage.warning(`临时添加危害因素：${lastWord}`);
        } else {
          createMessage.success(`已自动添加新的危害因素：${lastWord}`);
          // 重新加载数据以包含新添加的项
          await fetchRiskFactorList();
        }

    } catch (error: any) {
      console.error('自动添加危害因素失败:', error);
      createMessage.error('自动添加失败：' + (error?.message || '未知错误'));
    } finally {
      loading.value = false;
      isManualInput.value = false;
    }
  };

  // 组件挂载时加载初始数据
  onMounted(() => {
    fetchRiskFactorList();
  });

  // 暴露方法给父组件
  defineExpose({
    clearSelection: handleClear,
  });
</script>

<style lang="less" scoped>
  .zy-risk-factor-autocomplete {
    .risk-factor-option {
      display: flex;
      flex-direction: column;
      padding: 6px 0;

      .risk-factor-name {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 1.4;
        font-weight: 500;
      }

      .risk-factor-details {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 4px;

        .risk-factor-code {
          font-size: 12px;
          color: #1890ff;
          background: #f0f8ff;
          padding: 2px 6px;
          border-radius: 4px;
          line-height: 1.2;
        }

        .risk-factor-help-char {
          font-size: 12px;
          color: #52c41a;
          background: #f6ffed;
          padding: 2px 6px;
          border-radius: 4px;
          line-height: 1.2;
        }

        .risk-factor-wubi-char {
          font-size: 12px;
          color: #722ed1;
          background: #f9f0ff;
          padding: 2px 6px;
          border-radius: 4px;
          line-height: 1.2;
        }

        .risk-factor-harm-level {
          font-size: 12px;
          color: #fa8c16;
          background: #fff7e6;
          padding: 2px 6px;
          border-radius: 4px;
          line-height: 1.2;
        }
      }
    }


  }

  :deep(.ant-select-dropdown) {
    .ant-select-item-option-content {
      white-space: normal;
    }
  }
</style>

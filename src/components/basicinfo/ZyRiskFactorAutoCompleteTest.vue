<template>
  <div class="test-container">
    <h3>危害因素自动补全组件测试</h3>
    
    <div class="test-section">
      <h4>基础功能测试</h4>
      <ZyRiskFactorAutoComplete
        v-model:value="testValue1"
        placeholder="请输入危害因素名称进行测试"
        @change="handleChange1"
        @select="handleSelect1"
      />
      <p>当前值: {{ testValue1 }}</p>
      <p>选中的危害因素: {{ selectedFactor1 ? selectedFactor1.name : '无' }}</p>
      <p>是否新创建: {{ isNewlyCreated1 ? '是' : '否' }}</p>
    </div>

    <div class="test-section">
      <h4>自定义配置测试</h4>
      <ZyRiskFactorAutoComplete
        v-model:value="testValue2"
        placeholder="测试自定义配置"
        :config="customConfig"
        @change="handleChange2"
        @select="handleSelect2"
      />
      <p>当前值: {{ testValue2 }}</p>
      <p>选中的危害因素: {{ selectedFactor2 ? selectedFactor2.name : '无' }}</p>
      <p>是否新创建: {{ isNewlyCreated2 ? '是' : '否' }}</p>
    </div>

    <div class="test-section">
      <h4>多词输入测试</h4>
      <ZyRiskFactorAutoComplete
        v-model:value="testValue3"
        placeholder="测试多词输入，如：噪声 粉尘 高温"
        @change="handleChange3"
        @select="handleSelect3"
      />
      <p>当前值: {{ testValue3 }}</p>
      <p>选中的危害因素: {{ selectedFactor3 ? selectedFactor3.name : '无' }}</p>
      <p>是否新创建: {{ isNewlyCreated3 ? '是' : '否' }}</p>
      <div class="test-tips">
        <p><strong>测试说明：</strong></p>
        <ul>
          <li>输入多个词，用空格分隔，如："噪声 粉尘 高温"</li>
          <li>搜索时会以最后一个词进行筛选</li>
          <li>按空格键的行为：
            <ul>
              <li>如果有候选列表，会选中第一个选项</li>
              <li>如果候选列表为空，会自动添加最后一个词</li>
            </ul>
          </li>
          <li>选中后会替换最后一个词</li>
        </ul>
      </div>
    </div>

    <div class="test-section">
      <h4>空格键行为测试</h4>
      <ZyRiskFactorAutoComplete
        v-model:value="testValue4"
        placeholder="测试空格键行为：有候选时选中第一个，无候选时自动添加"
        @change="handleChange4"
        @select="handleSelect4"
      />
      <p>当前值: {{ testValue4 }}</p>
      <p>选中的危害因素: {{ selectedFactor4 ? selectedFactor4.name : '无' }}</p>
      <p>是否新创建: {{ isNewlyCreated4 ? '是' : '否' }}</p>
      <div class="test-tips">
        <p><strong>空格键测试步骤：</strong></p>
        <ul>
          <li>1. 输入已存在的危害因素名称（如"噪声"），按空格键 → 选中第一个候选项并添加空格，变成"噪声 "</li>
          <li>2. 继续输入下一个词（如"粉尘"），按空格键 → 选中候选项，变成"噪声 粉尘 "</li>
          <li>3. 输入不存在的名称（如"测试危害"），按空格键 → 自动添加新项并添加空格</li>
          <li>4. 测试连续输入多个词的完整流程</li>
        </ul>
        <p><strong>回车键测试步骤：</strong></p>
        <ul>
          <li>1. 输入"噪声 粉尘 新危害"，按回车键 → 应该替换最后一个词或自动添加，变成"噪声 粉尘 确定的名称"</li>
          <li>2. 不应该覆盖前面已有的内容</li>
          <li>3. selectedFactors数组应该包含所有危害因素，不只是最后一个</li>
        </ul>
        <p><strong>当前测试结果：</strong></p>
        <ul>
          <li>输入值: {{ testValue4 }}</li>
          <li>selectedFactors数量: {{ selectedFactor4 ? 1 : 0 }}</li>
          <li>selectedFactor: {{ selectedFactor4 ? selectedFactor4.name + ' (' + selectedFactor4.code + ')' : '无' }}</li>
        </ul>
        <p><strong>预期行为：</strong></p>
        <ul>
          <li>空格键选中后会保留空格，便于继续输入</li>
          <li>回车键/失焦会处理最后一个词，不覆盖前面内容</li>
          <li>点击候选列表选中：多词时替换最后一个词，单词时直接替换</li>
          <li>支持连续输入多个危害因素</li>
        </ul>
        <p><strong>点击选择测试：</strong></p>
        <ul>
          <li>输入"噪声 粉"，点击候选列表中的"粉尘" → 应该变成"噪声 粉尘"</li>
          <li>输入"噪"，点击候选列表中的"噪声" → 应该变成"噪声"</li>
        </ul>
      </div>
    </div>

    <div class="test-section">
      <h4>连续输入完整测试</h4>
      <ZyRiskFactorAutoComplete
        v-model:value="testValue5"
        placeholder="测试连续输入多个危害因素，用空格键选中"
        @change="handleChange5"
        @select="handleSelect5"
      />
      <p>当前值: {{ testValue5 }}</p>
      <p>选中的危害因素: {{ selectedFactor5 ? selectedFactor5.name : '无' }}</p>
      <p>是否新创建: {{ isNewlyCreated5 ? '是' : '否' }}</p>
      <div class="test-tips">
        <p><strong>完整流程测试：</strong></p>
        <ol>
          <li>输入"噪声"，按空格键 → 应该变成"噪声 "</li>
          <li>输入"粉尘"，按空格键 → 应该变成"噪声 粉尘 "</li>
          <li>输入"高温"，按空格键 → 应该变成"噪声 粉尘 高温 "</li>
          <li>输入"新危害因素"，按空格键 → 应该自动添加并变成"噪声 粉尘 高温 新危害因素 "</li>
        </ol>
      </div>
    </div>

    <div class="test-section">
      <h4>禁用自动添加测试</h4>
      <ZyRiskFactorAutoComplete
        v-model:value="testValue6"
        placeholder="禁用自动添加功能（适用于后端API不完整的情况）"
        :config="{ enableAutoAdd: false }"
        @change="handleChange6"
        @select="handleSelect6"
      />
      <p>当前值: {{ testValue6 }}</p>
      <p>选中的危害因素: {{ selectedFactor6 ? selectedFactor6.name : '无' }}</p>
      <div class="test-tips">
        <p><strong>说明：</strong></p>
        <ul>
          <li>当后端 getFallback API 不存在时，可以禁用自动添加功能</li>
          <li>这样组件仍然可以正常工作，只是不会自动创建新的危害因素</li>
        </ul>
      </div>
    </div>

    <div class="test-section">
      <h4>测试日志</h4>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          {{ log }}
        </div>
      </div>
      <a-button @click="clearLogs">清空日志</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import ZyRiskFactorAutoComplete from './ZyRiskFactorAutoComplete.vue';
  import type {
    ZyRiskFactorAutoCompleteDTO,
    ZyRiskFactorSimpleConfig,
    ZyRiskFactorSimpleChangeEvent,
  } from '/@/types/basicinfo/zyRiskFactor';

  // 测试数据
  const testValue1 = ref<string>('');
  const testValue2 = ref<string>('');
  const testValue3 = ref<string>('');
  const testValue4 = ref<string>('');
  const testValue5 = ref<string>('');
  const testValue6 = ref<string>('');

  const selectedFactor1 = ref<ZyRiskFactorAutoCompleteDTO | undefined>();
  const selectedFactor2 = ref<ZyRiskFactorAutoCompleteDTO | undefined>();
  const selectedFactor3 = ref<ZyRiskFactorAutoCompleteDTO | undefined>();
  const selectedFactor4 = ref<ZyRiskFactorAutoCompleteDTO | undefined>();
  const selectedFactor5 = ref<ZyRiskFactorAutoCompleteDTO | undefined>();
  const selectedFactor6 = ref<ZyRiskFactorAutoCompleteDTO | undefined>();

  const isNewlyCreated1 = ref<boolean>(false);
  const isNewlyCreated2 = ref<boolean>(false);
  const isNewlyCreated3 = ref<boolean>(false);
  const isNewlyCreated4 = ref<boolean>(false);
  const isNewlyCreated5 = ref<boolean>(false);

  const logs = ref<string[]>([]);

  // 自定义配置
  const customConfig: ZyRiskFactorSimpleConfig = {
    enableAutoAdd: true,
    searchDebounce: 500,
    maxOptions: 20,
    showCode: true,
    showHelpChar: false,
    showWubiChar: true,
  };

  // 添加日志
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    logs.value.unshift(`[${timestamp}] ${message}`);
    if (logs.value.length > 50) {
      logs.value = logs.value.slice(0, 50);
    }
  };

  // 事件处理函数
  const handleChange1 = (event: ZyRiskFactorSimpleChangeEvent) => {
    selectedFactor1.value = event.selectedFactor;
    isNewlyCreated1.value = event.isNewlyCreated || false;
    addLog(`测试1 - 值变化: ${event.value}, 新创建: ${event.isNewlyCreated || false}, 选中数量: ${event.selectedFactors?.length || 0}`);
  };

  const handleSelect1 = (value: string, option: ZyRiskFactorAutoCompleteDTO) => {
    addLog(`测试1 - 选择: ${option.name} (${option.code})`);
  };

  const handleChange2 = (event: ZyRiskFactorSimpleChangeEvent) => {
    selectedFactor2.value = event.selectedFactor;
    isNewlyCreated2.value = event.isNewlyCreated || false;
    addLog(`测试2 - 值变化: ${event.value}, 新创建: ${event.isNewlyCreated || false}`);
  };

  const handleSelect2 = (value: string, option: ZyRiskFactorAutoCompleteDTO) => {
    addLog(`测试2 - 选择: ${option.name} (${option.code})`);
  };

  const handleChange3 = (event: ZyRiskFactorSimpleChangeEvent) => {
    selectedFactor3.value = event.selectedFactor;
    isNewlyCreated3.value = event.isNewlyCreated || false;
    addLog(`测试3 - 多词输入值变化: ${event.value}, 新创建: ${event.isNewlyCreated || false}`);
  };

  const handleSelect3 = (value: string, option: ZyRiskFactorAutoCompleteDTO) => {
    addLog(`测试3 - 多词输入选择: ${option.name} (${option.code})`);
  };

  const handleChange4 = (event: ZyRiskFactorSimpleChangeEvent) => {
    selectedFactor4.value = event.selectedFactor;
    isNewlyCreated4.value = event.isNewlyCreated || false;
    addLog(`测试4 - 空格键行为值变化: ${event.value}, 新创建: ${event.isNewlyCreated || false}, selectedFactors数量: ${event.selectedFactors?.length || 0}`);
    console.log('完整的selectedFactors:', event.selectedFactors);
  };

  const handleSelect4 = (value: string, option: ZyRiskFactorAutoCompleteDTO) => {
    addLog(`测试4 - 空格键行为选择: ${option.name} (${option.code})`);
  };

  const handleChange5 = (event: ZyRiskFactorSimpleChangeEvent) => {
    selectedFactor5.value = event.selectedFactor;
    addLog(`测试5 - 禁用自动添加值变化: ${event.value}`);
  };

  const handleChange5 = (event: ZyRiskFactorSimpleChangeEvent) => {
    selectedFactor5.value = event.selectedFactor;
    isNewlyCreated5.value = event.isNewlyCreated || false;
    addLog(`测试5 - 连续输入值变化: ${event.value}, 新创建: ${event.isNewlyCreated || false}`);
  };

  const handleSelect5 = (value: string, option: ZyRiskFactorAutoCompleteDTO) => {
    addLog(`测试5 - 连续输入选择: ${option.name} (${option.code})`);
  };

  const handleChange6 = (event: ZyRiskFactorSimpleChangeEvent) => {
    selectedFactor6.value = event.selectedFactor;
    addLog(`测试6 - 禁用自动添加值变化: ${event.value}`);
  };

  const handleSelect6 = (value: string, option: ZyRiskFactorAutoCompleteDTO) => {
    addLog(`测试6 - 禁用自动添加选择: ${option.name} (${option.code})`);
  };

  const clearLogs = () => {
    logs.value = [];
  };
</script>

<style lang="less" scoped>
  .test-container {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
  }

  .test-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background: #fafafa;

    h4 {
      margin-top: 0;
      margin-bottom: 15px;
      color: #1890ff;
    }

    p {
      margin: 8px 0;
      font-size: 14px;
    }

    .test-tips {
      margin-top: 15px;
      padding: 10px;
      background: #e6f7ff;
      border: 1px solid #91d5ff;
      border-radius: 4px;

      p {
        margin: 0 0 8px 0;
        font-weight: bold;
        color: #1890ff;
      }

      ul {
        margin: 0;
        padding-left: 20px;

        li {
          margin: 4px 0;
          font-size: 13px;
          color: #666;
        }
      }
    }
  }

  .log-container {
    max-height: 200px;
    overflow-y: auto;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;

    .log-item {
      font-size: 12px;
      font-family: monospace;
      margin-bottom: 4px;
      padding: 2px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }
    }
  }
</style>

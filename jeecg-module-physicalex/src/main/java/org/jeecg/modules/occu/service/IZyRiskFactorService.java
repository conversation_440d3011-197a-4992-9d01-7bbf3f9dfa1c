package org.jeecg.modules.occu.service;

import org.jeecg.modules.occu.dto.ZyRiskFactorAutoCompleteDTO;
import org.jeecg.modules.occu.entity.ZyRiskFactorItemgroup;
import org.jeecg.modules.occu.entity.ZyRiskFactorWorktype;
import org.jeecg.modules.occu.entity.ZyRiskFactor;
import com.baomidou.mybatisplus.extension.service.IService;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 危害因素
 * @Author: jeecg-boot
 * @Date: 2025-02-18
 * @Version: V1.0
 */
public interface IZyRiskFactorService extends IService<ZyRiskFactor> {

    String getCodeByName(String name);

    List<String> listCodesByNames(List<String> names);

    List<ZyRiskFactor> listByCodes(List<String> codes);

    /**
     * 获取自动完成列表
     *
     * @param keyword    搜索关键词
     * @param searchType 搜索类型
     * @param pageSize   返回数量限制
     * @return 危害因素列表
     */
    List<ZyRiskFactorAutoCompleteDTO> getAutoCompleteList(String keyword, String searchType, Integer pageSize);

    /**
     * 精确匹配危害因素名称
     *
     * @param name 危害因素名称
     * @return 危害因素信息
     */
    ZyRiskFactorAutoCompleteDTO getByExactName(String name);

    /**
     * 根据名称列表获取危害因素
     *
     * @param names 危害因素名称列表
     * @return 危害因素列表
     */
    List<ZyRiskFactorAutoCompleteDTO> getByNames(List<String> names);

    /**
     * 批量验证危害因素名称
     *
     * @param names 危害因素名称列表
     * @return 验证结果
     */
    BatchValidationResult batchValidateNames(List<String> names);

    /**
     * 获取fallback模板数据（用作新增危害因素的模板）
     *
     * @return fallback模板数据
     */
    ZyRiskFactorAutoCompleteDTO getFallbackTemplate();

    /**
     * 批量验证结果类
     */
    class BatchValidationResult {
        private List<ValidationResult> results;
        private Integer successCount;
        private Integer failureCount;
        private List<ZyRiskFactorAutoCompleteDTO> matchedFactors;

        // 构造函数
        public BatchValidationResult() {
        }

        public BatchValidationResult(List<ValidationResult> results, Integer successCount, Integer failureCount, List<ZyRiskFactorAutoCompleteDTO> matchedFactors) {
            this.results = results;
            this.successCount = successCount;
            this.failureCount = failureCount;
            this.matchedFactors = matchedFactors;
        }

        // Getters and Setters
        public List<ValidationResult> getResults() {
            return results;
        }

        public void setResults(List<ValidationResult> results) {
            this.results = results;
        }

        public Integer getSuccessCount() {
            return successCount;
        }

        public void setSuccessCount(Integer successCount) {
            this.successCount = successCount;
        }

        public Integer getFailureCount() {
            return failureCount;
        }

        public void setFailureCount(Integer failureCount) {
            this.failureCount = failureCount;
        }

        public List<ZyRiskFactorAutoCompleteDTO> getMatchedFactors() {
            return matchedFactors;
        }

        public void setMatchedFactors(List<ZyRiskFactorAutoCompleteDTO> matchedFactors) {
            this.matchedFactors = matchedFactors;
        }
    }

    /**
     * 验证结果类
     */
    class ValidationResult {
        private String inputName;
        private Boolean found;
        private ZyRiskFactorAutoCompleteDTO matchedFactor;
        private String errorMessage;

        // 构造函数
        public ValidationResult() {
        }

        public ValidationResult(String inputName, Boolean found, ZyRiskFactorAutoCompleteDTO matchedFactor, String errorMessage) {
            this.inputName = inputName;
            this.found = found;
            this.matchedFactor = matchedFactor;
            this.errorMessage = errorMessage;
        }

        // Getters and Setters
        public String getInputName() {
            return inputName;
        }

        public void setInputName(String inputName) {
            this.inputName = inputName;
        }

        public Boolean getFound() {
            return found;
        }

        public void setFound(Boolean found) {
            this.found = found;
        }

        public ZyRiskFactorAutoCompleteDTO getMatchedFactor() {
            return matchedFactor;
        }

        public void setMatchedFactor(ZyRiskFactorAutoCompleteDTO matchedFactor) {
            this.matchedFactor = matchedFactor;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
    }

    /**
     * 添加一对多(仅必检项目)
     *
     * @param zyRiskFactor
     * @param zyRiskFactorItemgroupList
     */
    void saveMain(ZyRiskFactor zyRiskFactor, List<ZyRiskFactorItemgroup> zyRiskFactorItemgroupList);

    /**
     * 添加一对多（包含工种关联）
     *
     * @param zyRiskFactor              主表
     * @param zyRiskFactorItemgroupList 必检项目
     * @param zyRiskFactorWorktypeList  工种关联
     */
    void saveMain(ZyRiskFactor zyRiskFactor, List<ZyRiskFactorItemgroup> zyRiskFactorItemgroupList, List<ZyRiskFactorWorktype> zyRiskFactorWorktypeList);

    /**
     * 修改一对多
     *
     * @param zyRiskFactor
     * @param zyRiskFactorItemgroupList
     */
    void updateMain(ZyRiskFactor zyRiskFactor, List<ZyRiskFactorItemgroup> zyRiskFactorItemgroupList);

    /**
     * 修改一对多（包含工种关联）
     *
     * @param zyRiskFactor
     * @param zyRiskFactorItemgroupList
     * @param zyRiskFactorWorktypeList
     */
    void updateMain(ZyRiskFactor zyRiskFactor, List<ZyRiskFactorItemgroup> zyRiskFactorItemgroupList, List<ZyRiskFactorWorktype> zyRiskFactorWorktypeList);

    /**
     * 删除一对多
     *
     * @param id
     */
    void delMain(String id);

    /**
     * 批量删除一对多
     *
     * @param idList
     */
    void delBatchMain(Collection<? extends Serializable> idList);

    /**
     * 根据工种ID查询关联的危害因素列表
     *
     * @param worktypeId 工种ID
     * @return 危害因素列表
     */
    List<ZyRiskFactor> getRiskFactorsByWorktype(String worktypeId);


    List<ZyRiskFactor> getRiskFactorsByWorktypeCode(String code);
}

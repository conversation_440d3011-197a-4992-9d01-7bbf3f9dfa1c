package org.jeecg.modules.occu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.basicinfo.entity.CheckPartDict;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.mapper.CheckPartDictMapper;
import org.jeecg.modules.basicinfo.mapper.ItemGroupMapper;
import org.jeecg.modules.occu.dto.ZyRiskFactorAutoCompleteDTO;
import org.jeecg.modules.occu.entity.ZyRiskFactor;
import org.jeecg.modules.occu.entity.ZyRiskFactorItemgroup;
import org.jeecg.modules.occu.entity.ZyRiskFactorWorktype;
import org.jeecg.modules.occu.entity.ZyWorktype;
import org.jeecg.modules.occu.mapper.ZyRiskFactorItemgroupMapper;
import org.jeecg.modules.occu.mapper.ZyRiskFactorMapper;
import org.jeecg.modules.occu.mapper.ZyRiskFactorWorktypeMapper;
import org.jeecg.modules.occu.mapper.ZyWorktypeMapper;
import org.jeecg.modules.occu.service.IZyRiskFactorService;
import org.jeecg.modules.basicinfo.util.PinyinUtil;
import org.jeecg.modules.basicinfo.util.WubiUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 危害因素 Service
 */
@Slf4j
@Service
@CacheConfig(cacheNames = "zyRiskFactor")
public class ZyRiskFactorServiceImpl extends ServiceImpl<ZyRiskFactorMapper, ZyRiskFactor> implements IZyRiskFactorService {

    @Autowired
    private ZyRiskFactorMapper zyRiskFactorMapper;
    @Autowired
    private ZyRiskFactorItemgroupMapper zyRiskFactorItemgroupMapper;
    @Autowired
    private ZyRiskFactorWorktypeMapper zyRiskFactorWorktypeMapper;
    @Autowired
    private ZyWorktypeMapper zyWorktypeMapper;
    @Autowired
    private ItemGroupMapper itemGroupMapper;
    @Autowired
    private CheckPartDictMapper checkPartDictMapper;

    @Override
    @Cacheable(value = "riskFactorCache", key = "#name", unless = "#result == null")
    public String getCodeByName(String name) {

        String trimmedName = name.trim();
        return queryCodeByName(trimmedName);
    }


    @Override
    @Cacheable(key = "#names.toString()", unless = "#result.isEmpty()")
    public List<String> listCodesByNames(List<String> names) {
        log.info("根据名称列表获取危害因素代码，输入名称数量: {}", names != null ? names.size() : 0);

        if (names == null || names.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> codes = new ArrayList<>();

        for (String name : names) {
            if (!StringUtils.hasText(name)) {
                continue;
            }

            String trimmedName = name.trim();
            String code = queryCodeByName(trimmedName);

            if (StringUtils.hasText(code)) {
                codes.add(code);
            }
        }

        log.info("根据名称列表获取危害因素代码完成，输入: {}个，输出: {}个", names.size(), codes.size());
        return codes;
    }

    /**
     * 根据单个名称获取危害因素代码
     * 匹配规则：
     * 1. 优先精确匹配
     * 2. 如果没有精确匹配，进行模糊匹配
     * 3. 如果模糊匹配只有一个结果，返回该结果
     * 4. 如果有多个模糊匹配结果，优先选择 fallBackFlag=1 的记录，否则选择排序最靠前的
     *
     * @param name 危害因素名称
     * @return 危害因素代码，如果未找到返回null
     */
    @Cacheable(key = "'single:' + #name", unless = "#result == null")
    public String queryCodeByName(String name) {
        try {
            // 1. 尝试精确匹配
            ZyRiskFactorAutoCompleteDTO exactMatch = getByExactName(name);
            if (exactMatch != null && StringUtils.hasText(exactMatch.getCode())) {
                log.debug("精确匹配到危害因素: {} -> {}", name, exactMatch.getCode());
                return exactMatch.getCode();
            }

            // 2. 进行模糊匹配
            List<ZyRiskFactorAutoCompleteDTO> fuzzyMatches = getAutoCompleteList(name, "name", 10);

            if (fuzzyMatches == null || fuzzyMatches.isEmpty()) {
                log.debug("未找到匹配的危害因素: {}", name);
                return null;
            }

            // 3. 如果只有一个模糊匹配结果，直接返回
            if (fuzzyMatches.size() == 1) {
                ZyRiskFactorAutoCompleteDTO match = fuzzyMatches.get(0);
                if (StringUtils.hasText(match.getCode())) {
                    log.debug("模糊匹配到唯一危害因素: {} -> {}", name, match.getCode());
                    return match.getCode();
                }
            }

            // 4. 如果有多个模糊匹配结果，查找 fallBackFlag=1 的记录
            ZyRiskFactorAutoCompleteDTO fallbackMatch = getFallbackRiskFactorByName();
            if (fallbackMatch != null && StringUtils.hasText(fallbackMatch.getCode())) {
                log.debug("模糊匹配到多个结果，找到备用标志记录: {} -> {} (共{}个候选)", name, fallbackMatch.getCode(), fuzzyMatches.size());
                return fallbackMatch.getCode();
            }


            log.debug("未找到有效的危害因素代码: {}", name);
            return null;
        } catch (Exception e) {
            log.error("根据名称获取危害因素代码失败: {}", name, e);
            return null;
        }
    }

    /**
     * 根据名称查找 fallBackFlag=1 的危害因素记录
     *
     * @return 备用标志的危害因素记录，如果未找到返回null
     */
    @Cacheable(key = "'fallback:riskFactor' ", unless = "#result == null")
    public ZyRiskFactorAutoCompleteDTO getFallbackRiskFactorByName() {
        try {
            return zyRiskFactorMapper.getFallback();
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<ZyRiskFactor> listByCodes(List<String> codes) {
        if (codes == null || codes.isEmpty()) {
            return new ArrayList<>();
        }
        return zyRiskFactorMapper.selectList(new LambdaQueryWrapper<ZyRiskFactor>().in(ZyRiskFactor::getCode, codes));
    }

    @Override
    @Cacheable(key = "'autocomplete:' + (#keyword != null ? #keyword : 'all') + ':' + (#searchType != null ? #searchType : 'all') + ':' + (#pageSize != null ? #pageSize : 50)", unless = "#result == null || #result.isEmpty()")
    public List<ZyRiskFactorAutoCompleteDTO> getAutoCompleteList(String keyword, String searchType, Integer pageSize) {
        log.info("危害因素自动完成查询，关键词: {}, 搜索类型: {}, 数量限制: {}", keyword, searchType, pageSize);

        try {
            // 参数校验和默认值设置
            if (pageSize == null || pageSize <= 0) {
                pageSize = 50;
            }
            if (pageSize > 100) {
                pageSize = 100; // 限制最大返回数量
            }

            if (!StringUtils.hasText(searchType)) {
                searchType = "all";
            }

            // 关键词预处理
            String processedKeyword = null;
            if (StringUtils.hasText(keyword)) {
                processedKeyword = keyword.trim();
                // 防止SQL注入，移除特殊字符
                processedKeyword = processedKeyword.replaceAll("[';\"\\\\]", "");
                if (processedKeyword.length() > 50) {
                    processedKeyword = processedKeyword.substring(0, 50);
                }
            }

            List<ZyRiskFactorAutoCompleteDTO> results = zyRiskFactorMapper.getAutoCompleteList(processedKeyword, searchType, pageSize);

            // 数据质量检查和过滤
            List<ZyRiskFactorAutoCompleteDTO> filteredResults = results.stream().filter(this::isValidRiskFactor).collect(Collectors.toList());

            log.info("危害因素自动完成查询完成，原始结果: {}条，过滤后: {}条", results.size(), filteredResults.size());

            return filteredResults;

        } catch (Exception e) {
            log.error("危害因素自动完成查询失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    @Cacheable(key = "'exact:' + #name", unless = "#result == null")
    public ZyRiskFactorAutoCompleteDTO getByExactName(String name) {
        log.info("精确匹配危害因素名称: {}", name);

        try {
            if (!StringUtils.hasText(name)) {
                return null;
            }

            String trimmedName = name.trim();
            ZyRiskFactorAutoCompleteDTO result = zyRiskFactorMapper.getByExactName(trimmedName);

            // 验证结果的有效性
            if (result != null && !isValidRiskFactor(result)) {
                log.warn("精确匹配到无效的危害因素数据: {}", result);
                return null;
            }

            log.info("精确匹配结果: {}", result != null ? "找到" : "未找到");
            return result;

        } catch (Exception e) {
            log.error("精确匹配危害因素名称失败", e);
            return null;
        }
    }

    @Override
    public List<ZyRiskFactorAutoCompleteDTO> getByNames(List<String> names) {
        log.info("根据名称列表获取危害因素，数量: {}", names != null ? names.size() : 0);

        try {
            if (names == null || names.isEmpty()) {
                return new ArrayList<>();
            }

            // 预处理名称列表
            List<String> processedNames = names.stream().filter(StringUtils::hasText).map(String::trim).distinct().limit(50) // 限制最大查询数量
                    .collect(Collectors.toList());

            if (processedNames.isEmpty()) {
                return new ArrayList<>();
            }

            List<ZyRiskFactorAutoCompleteDTO> results = zyRiskFactorMapper.getByNames(processedNames);

            // 数据质量检查和过滤
            List<ZyRiskFactorAutoCompleteDTO> filteredResults = results.stream().filter(this::isValidRiskFactor).collect(Collectors.toList());

            log.info("根据名称列表查询完成，原始结果: {}条，过滤后: {}条", results.size(), filteredResults.size());

            return filteredResults;

        } catch (Exception e) {
            log.error("根据名称列表获取危害因素失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public BatchValidationResult batchValidateNames(List<String> names) {
        log.info("批量验证危害因素名称，数量: {}", names != null ? names.size() : 0);

        try {
            if (names == null || names.isEmpty()) {
                return new BatchValidationResult(new ArrayList<>(), 0, 0, new ArrayList<>());
            }

            // 预处理名称列表
            List<String> processedNames = names.stream().filter(StringUtils::hasText).map(String::trim).distinct().limit(50) // 限制最大验证数量
                    .collect(Collectors.toList());

            List<ValidationResult> results = new ArrayList<>();
            List<ZyRiskFactorAutoCompleteDTO> matchedFactors = new ArrayList<>();
            int successCount = 0;

            for (String name : processedNames) {
                try {
                    ZyRiskFactorAutoCompleteDTO matched = getByExactName(name);

                    if (matched != null) {
                        results.add(new ValidationResult(name, true, matched, null));
                        matchedFactors.add(matched);
                        successCount++;
                    } else {
                        results.add(new ValidationResult(name, false, null, "未找到匹配的危害因素"));
                    }
                } catch (Exception e) {
                    log.error("验证危害因素名称失败: {}", name, e);
                    results.add(new ValidationResult(name, false, null, "验证失败: " + e.getMessage()));
                }
            }

            int failureCount = processedNames.size() - successCount;

            log.info("批量验证完成，成功: {}，失败: {}", successCount, failureCount);

            return new BatchValidationResult(results, successCount, failureCount, matchedFactors);

        } catch (Exception e) {
            log.error("批量验证危害因素名称失败", e);
            return new BatchValidationResult(new ArrayList<>(), 0, names != null ? names.size() : 0, new ArrayList<>());
        }
    }

    /**
     * 验证危害因素数据的有效性
     *
     * @param factor 危害因素数据
     * @return 是否有效
     */
    private boolean isValidRiskFactor(ZyRiskFactorAutoCompleteDTO factor) {
        if (factor == null) {
            return false;
        }

        // 检查必要字段
        if (!StringUtils.hasText(factor.getId()) || !StringUtils.hasText(factor.getName())) {
            log.warn("危害因素缺少必要字段: {}", factor);
            return false;
        }

        String name = factor.getName().trim();

        // 检查名称长度
        if (name.length() < 2 || name.length() > 50) {
            log.warn("危害因素名称长度异常: {}", factor);
            return false;
        }

        // 检查是否名称与助记码、五笔码、编码相同（可能是数据错误）
        if (StringUtils.hasText(factor.getHelpChar()) && name.equalsIgnoreCase(factor.getHelpChar().trim())) {
            log.warn("危害因素名称与助记码相同，可能是数据错误: {}", factor);
            return false;
        }

        if (StringUtils.hasText(factor.getWubiChar()) && name.equalsIgnoreCase(factor.getWubiChar().trim())) {
            log.warn("危害因素名称与五笔码相同，可能是数据错误: {}", factor);
            return false;
        }

        if (StringUtils.hasText(factor.getCode()) && name.equalsIgnoreCase(factor.getCode().trim())) {
            log.warn("危害因素名称与编码相同，可能是数据错误: {}", factor);
            return false;
        }

        // 检查是否是纯字母（可能是助记码或五笔码被误当作名称）
        if (name.matches("^[a-zA-Z]{1,6}$")) {
            log.warn("危害因素名称疑似助记码或五笔码: {}", factor);
            return false;
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public void saveMain(ZyRiskFactor zyRiskFactor, List<ZyRiskFactorItemgroup> zyRiskFactorItemgroupList) {
        // 自动生成助记码和五笔简码
        generateHelpCharAndWubiCode(zyRiskFactor);
        zyRiskFactorMapper.insert(zyRiskFactor);
        if (zyRiskFactorItemgroupList != null && !zyRiskFactorItemgroupList.isEmpty()) {
            for (ZyRiskFactorItemgroup entity : zyRiskFactorItemgroupList) {
                entity.setFactorId(zyRiskFactor.getId());
                entity.setFactorName(zyRiskFactor.getName());
                entity.setFactorCode(zyRiskFactor.getCode());
                // 补充相关信息
                enrichItemgroupInfo(entity, zyRiskFactor.getName());
                zyRiskFactorItemgroupMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public void saveMain(ZyRiskFactor zyRiskFactor, List<ZyRiskFactorItemgroup> zyRiskFactorItemgroupList, List<ZyRiskFactorWorktype> zyRiskFactorWorktypeList) {
        // 自动生成助记码和五笔简码
        generateHelpCharAndWubiCode(zyRiskFactor);
        zyRiskFactorMapper.insert(zyRiskFactor);
        // 必检项目
        if (zyRiskFactorItemgroupList != null && !zyRiskFactorItemgroupList.isEmpty()) {
            for (ZyRiskFactorItemgroup entity : zyRiskFactorItemgroupList) {
                entity.setFactorId(zyRiskFactor.getId());
                entity.setFactorName(zyRiskFactor.getName());
                entity.setFactorCode(zyRiskFactor.getCode());
                // 补充相关信息
                enrichItemgroupInfo(entity, zyRiskFactor.getName());
                zyRiskFactorItemgroupMapper.insert(entity);
            }
        }
        // 工种关联
        if (zyRiskFactorWorktypeList != null && !zyRiskFactorWorktypeList.isEmpty()) {
            for (ZyRiskFactorWorktype entity : zyRiskFactorWorktypeList) {
                ZyWorktype worktype = zyWorktypeMapper.selectById(entity.getWorktypeId());
                entity.setFactorId(zyRiskFactor.getId());
                entity.setFactorName(zyRiskFactor.getName());
                entity.setFactorCode(zyRiskFactor.getCode());

                entity.setWorktypeId(worktype.getId());
                entity.setWorktypeName(worktype.getName());
                entity.setWorktypeCode(worktype.getCode());
                zyRiskFactorWorktypeMapper.insert(entity);
            }
        }
    }

    private Map<String, String> buildWorktypeCodeMap(List<ZyRiskFactorWorktype> list) {
        Set<String> ids = list.stream().map(ZyRiskFactorWorktype::getWorktypeId).filter(org.apache.commons.lang3.StringUtils::isNotBlank).collect(Collectors.toSet());
        if (ids.isEmpty()) return new HashMap<>();
        return zyWorktypeMapper.selectBatchIds(ids).stream().filter(w -> w.getId() != null).collect(Collectors.toMap(w -> w.getId(), w -> w.getCode(), (a, b) -> a));
    }

    /**
     * 补充必检项目的相关信息
     *
     * @param entity     必检项目实体
     * @param factorName 危害因素名称
     */
    private void enrichItemgroupInfo(ZyRiskFactorItemgroup entity, String factorName) {
        // 设置危害因素名称
        entity.setFactorName(factorName);

        // 查询并设置项目组合信息
        if (org.apache.commons.lang3.StringUtils.isNotBlank(entity.getItemgroupId())) {
            ItemGroup itemGroup = itemGroupMapper.selectById(entity.getItemgroupId());
            if (itemGroup != null) {
                entity.setGroupName(org.apache.commons.lang3.StringUtils.isNotBlank(itemGroup.getHisName()) ? itemGroup.getHisName() : itemGroup.getName());
                entity.setGroupCode(itemGroup.getHisCode());
            }
        }

        // 查询并设置检查部位信息
        if (org.apache.commons.lang3.StringUtils.isNotBlank(entity.getCheckPartCode())) {
            LambdaQueryWrapper<CheckPartDict> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CheckPartDict::getCode, entity.getCheckPartCode());
            CheckPartDict checkPart = checkPartDictMapper.selectOne(wrapper);
            if (checkPart != null) {
                entity.setCheckPartId(checkPart.getId());
                entity.setCheckPartName(checkPart.getName());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public void updateMain(ZyRiskFactor zyRiskFactor, List<ZyRiskFactorItemgroup> zyRiskFactorItemgroupList) {
        // 自动生成助记码和五笔简码
        generateHelpCharAndWubiCode(zyRiskFactor);
        zyRiskFactorMapper.updateById(zyRiskFactor);
        zyRiskFactorItemgroupMapper.deleteByMainId(zyRiskFactor.getId());
        if (zyRiskFactorItemgroupList != null && !zyRiskFactorItemgroupList.isEmpty()) {
            for (ZyRiskFactorItemgroup entity : zyRiskFactorItemgroupList) {
                entity.setFactorId(zyRiskFactor.getId());
                entity.setFactorName(zyRiskFactor.getName());
                entity.setFactorCode(zyRiskFactor.getCode());
                // 补充相关信息
                enrichItemgroupInfo(entity, zyRiskFactor.getName());
                zyRiskFactorItemgroupMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public void updateMain(ZyRiskFactor zyRiskFactor, List<ZyRiskFactorItemgroup> zyRiskFactorItemgroupList, List<ZyRiskFactorWorktype> zyRiskFactorWorktypeList) {
        // 自动生成助记码和五笔简码
        generateHelpCharAndWubiCode(zyRiskFactor);
        zyRiskFactorMapper.updateById(zyRiskFactor);
        // 删除旧子表
        zyRiskFactorItemgroupMapper.deleteByMainId(zyRiskFactor.getId());
        zyRiskFactorWorktypeMapper.deleteByMainId(zyRiskFactor.getId());
        // 重新插入必检项目
        if (zyRiskFactorItemgroupList != null && !zyRiskFactorItemgroupList.isEmpty()) {
            for (ZyRiskFactorItemgroup entity : zyRiskFactorItemgroupList) {
                entity.setFactorId(zyRiskFactor.getId());
                entity.setFactorName(zyRiskFactor.getName());
                entity.setFactorCode(zyRiskFactor.getCode());
                // 补充相关信息
                enrichItemgroupInfo(entity, zyRiskFactor.getName());
                zyRiskFactorItemgroupMapper.insert(entity);
            }
        }
        // 重新插入工种关联并补充代码
        if (zyRiskFactorWorktypeList != null && !zyRiskFactorWorktypeList.isEmpty()) {
            Map<String, String> worktypeCodeMap = buildWorktypeCodeMap(zyRiskFactorWorktypeList);
            for (ZyRiskFactorWorktype entity : zyRiskFactorWorktypeList) {
                ZyWorktype worktype = zyWorktypeMapper.selectById(entity.getWorktypeId());
                entity.setFactorId(zyRiskFactor.getId());
                entity.setFactorName(zyRiskFactor.getName());
                entity.setFactorCode(zyRiskFactor.getCode());
                entity.setWorktypeCode(worktype.getCode());
                entity.setWorktypeName(worktype.getName());
                entity.setWorktypeId(worktype.getId());
                zyRiskFactorWorktypeMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public void delMain(String id) {
        zyRiskFactorItemgroupMapper.deleteByMainId(id);
        zyRiskFactorWorktypeMapper.deleteByMainId(id);
        zyRiskFactorMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public void delBatchMain(Collection<? extends Serializable> idList) {
        for (Serializable id : idList) {
            zyRiskFactorItemgroupMapper.deleteByMainId(id.toString());
            zyRiskFactorWorktypeMapper.deleteByMainId(id.toString());
            zyRiskFactorMapper.deleteById(id);
        }
    }

    @Override
    public List<ZyRiskFactor> getRiskFactorsByWorktype(String worktypeId) {
        if (org.apache.commons.lang3.StringUtils.isBlank(worktypeId)) {
            return new ArrayList<>();
        }
        List<ZyRiskFactorWorktype> worktypeList = zyRiskFactorWorktypeMapper.selectList(new LambdaQueryWrapper<ZyRiskFactorWorktype>().eq(ZyRiskFactorWorktype::getWorktypeId, worktypeId).orderByAsc(ZyRiskFactorWorktype::getSeq));
        if (worktypeList.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> factorIds = worktypeList.stream().map(ZyRiskFactorWorktype::getFactorId).collect(Collectors.toList());
        return zyRiskFactorMapper.selectBatchIds(factorIds);
    }


    @Override
    public List<ZyRiskFactor> getRiskFactorsByWorktypeCode(String workTypeCode) {
        if (org.apache.commons.lang3.StringUtils.isBlank(workTypeCode)) {
            return new ArrayList<>();
        }
        List<ZyRiskFactorWorktype> worktypeList = zyRiskFactorWorktypeMapper.selectList(new LambdaQueryWrapper<ZyRiskFactorWorktype>().eq(ZyRiskFactorWorktype::getWorktypeCode, workTypeCode).orderByAsc(ZyRiskFactorWorktype::getSeq));
        if (worktypeList.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> factorIds = worktypeList.stream().map(ZyRiskFactorWorktype::getFactorId).collect(Collectors.toList());
        return zyRiskFactorMapper.selectBatchIds(factorIds);
    }

    @Override
    @Cacheable(key = "'fallback:template'", unless = "#result == null")
    public ZyRiskFactorAutoCompleteDTO getFallbackTemplate() {
        log.info("获取fallback危害因素模板");

        try {
            // 查询fallback_flag=1的危害因素作为模板
            LambdaQueryWrapper<ZyRiskFactor> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ZyRiskFactor::getFallbackFlag, "1")
                       .eq(ZyRiskFactor::getValid, 1) // 启用状态
                       .orderByAsc(ZyRiskFactor::getSort)
                       .last("LIMIT 1");

            ZyRiskFactor fallbackFactor = zyRiskFactorMapper.selectOne(queryWrapper);

            if (fallbackFactor != null) {
                log.info("找到fallback模板: {}", fallbackFactor.getName());

                ZyRiskFactorAutoCompleteDTO dto = new ZyRiskFactorAutoCompleteDTO();
                dto.setId(fallbackFactor.getId());
                dto.setName(fallbackFactor.getName());
                dto.setCode(fallbackFactor.getCode());
                dto.setHelpChar(fallbackFactor.getHelpChar());
                dto.setWubiChar(fallbackFactor.getWubiCode());

                return dto;
            } else {
                log.warn("未找到fallback_flag=1的危害因素，返回null");
                return null;
            }
        } catch (Exception e) {
            log.error("获取fallback模板失败", e);
            return null;
        }
    }

    /**
     * 重写save方法，添加缓存清除和自动生成逻辑
     */
    @Override
    @CacheEvict(allEntries = true)
    public boolean save(ZyRiskFactor entity) {
        // 自动生成助记码和五笔简码
        generateHelpCharAndWubiCode(entity);
        return super.save(entity);
    }

    /**
     * 重写updateById方法，添加缓存清除和自动生成逻辑
     */
    @Override
    @CacheEvict(allEntries = true)
    public boolean updateById(ZyRiskFactor entity) {
        // 自动生成助记码和五笔简码
        generateHelpCharAndWubiCode(entity);
        return super.updateById(entity);
    }

    /**
     * 重写saveOrUpdate方法，添加缓存清除
     */
    @Override
    @CacheEvict(allEntries = true)
    public boolean saveOrUpdate(ZyRiskFactor entity) {
        // 自动生成助记码和五笔简码
        generateHelpCharAndWubiCode(entity);
        return super.saveOrUpdate(entity);
    }

    /**
     * 自动生成助记码和五笔简码
     *
     * @param zyRiskFactor 危害因素实体
     */
    private void generateHelpCharAndWubiCode(ZyRiskFactor zyRiskFactor) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(zyRiskFactor.getName())) {
            // 如果助记码为空，自动生成拼音首字母
            if (org.apache.commons.lang3.StringUtils.isBlank(zyRiskFactor.getHelpChar())) {
                zyRiskFactor.setHelpChar(PinyinUtil.generateSmartHelpChar(zyRiskFactor.getName()));
            }
            // 如果五笔简码为空，自动生成五笔简码
            if (org.apache.commons.lang3.StringUtils.isBlank(zyRiskFactor.getWubiCode())) {
                zyRiskFactor.setWubiCode(WubiUtil.generateSmartWubiCode(zyRiskFactor.getName()));
            }
        }
    }

}
